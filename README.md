# Heart Attack - Multi-Vendor Food Delivery Admin Panel

A comprehensive React-based admin panel for managing a multi-vendor food delivery platform. This system supports multiple user roles and provides complete management capabilities for restaurants, orders, users, and delivery operations.

## 🏗️ Architecture Overview

The admin panel is built with React 17 and uses Apollo Client for GraphQL API communication. It supports three main user types:
- **Super Admin**: Full system access and management
- **Vendor**: Restaurant owner with limited access to their own restaurants
- **Restaurant Admin**: Individual restaurant management

## 🚀 Tech Stack

- **Frontend**: React 17, Material-UI v5, Apollo Client
- **State Management**: Apollo Client Cache + Local Storage
- **Routing**: React Router DOM v5
- **Charts**: Chart.js with react-chartjs-2
- **Maps**: Google Maps API
- **Authentication**: JWT-based with role-based access control
- **Notifications**: Firebase Cloud Messaging
- **Styling**: Material-UI + Custom SCSS
- **Build Tool**: Create React App (CRA)

## 📱 User Roles & Access Levels

### Super Admin (`/super_admin`)
Full platform management with access to all features and analytics.

### Vendor (`/restaurant`)
Restaurant owners with access to their own restaurant management.

### Restaurant Admin (`/admin`)
Individual restaurant staff with operational access.

## 📄 Pages & Features

### 🏠 Super Admin Dashboard (`/super_admin/dashboard`)
**Endpoints**:
- `getDashboardTotal` - Overall platform statistics
- `getDashboardSales` - Sales analytics
- `getDashboardOrders` - Order analytics
- `getOrdersByDateRange` - Filtered order data

**Features**:
- Platform-wide statistics (users, vendors, restaurants, riders)
- Revenue analytics and charts
- Order status distribution
- Cash on delivery vs online payment metrics
- Date range filtering for analytics

### 👥 Vendors Management (`/super_admin/vendors`)
**Endpoints**:
- `getVendors` - List all vendors
- `createVendor` - Add new vendor
- `editVendor` - Update vendor details
- `deleteVendor` - Remove vendor

**Features**:
- View all registered vendors
- Create new vendor accounts
- Edit vendor information (email, restaurants)
- Delete vendor accounts
- Search and filter vendors

### 🏪 Restaurants Management (`/super_admin/restaurants`)
**Endpoints**:
- `restaurants` - List all restaurants
- `getRestaurantProfile` - Individual restaurant details
- `editRestaurant` - Update restaurant information

**Features**:
- View all restaurants across the platform
- Restaurant details (name, address, delivery time, minimum order)
- Commission rate management
- Restaurant status (active/inactive)
- Owner information and contact details

### 📦 All Orders (`/super_admin/all-orders`)
**Endpoints**:
- `allOrders` - Platform-wide order management
- `updateStatus` - Change order status
- `assignRider` - Assign delivery rider
- `updatePaymentStatus` - Update payment status

**Features**:
- View all orders across all restaurants
- Order status management (pending, confirmed, delivered, etc.)
- Payment status tracking
- Rider assignment
- Order details and customer information

### 👤 Users Management (`/super_admin/users`)
**Endpoints**:
- `getUsers` - List all platform users

**Features**:
- View all registered customers
- User contact information (name, email, phone)
- User addresses and delivery locations
- Search and filter users

### 🚴 Riders Management (`/super_admin/riders`)
**Endpoints**:
- `getRiders` - List all delivery riders
- `createRider` - Add new rider
- `editRider` - Update rider details
- `deleteRider` - Remove rider
- `toggleAvailablity` - Toggle rider availability

**Features**:
- Manage delivery riders
- Rider availability status
- Zone assignment
- Contact information management
- Performance tracking

### 🗺️ Zones Management (`/super_admin/zones`)
**Endpoints**:
- `getZones` - List delivery zones
- `createZone` - Add new delivery zone
- `editZone` - Update zone details
- `deleteZone` - Remove zone

**Features**:
- Define delivery zones
- Geographic boundary management
- Zone activation/deactivation
- Rider assignment to zones

### 🎫 Coupons Management (`/super_admin/coupons`)
**Endpoints**:
- `getCoupons` - List all coupons
- `createCoupon` - Create new coupon
- `editCoupon` - Update coupon details
- `deleteCoupon` - Remove coupon

**Features**:
- Create and manage discount coupons
- Set discount percentages
- Enable/disable coupons
- Coupon usage tracking

### 🍽️ Cuisines Management (`/super_admin/cuisines`)
**Endpoints**:
- `getCuisines` - List all cuisine types
- `createCuisine` - Add new cuisine
- `editCuisine` - Update cuisine details
- `deleteCuisine` - Remove cuisine

**Features**:
- Manage cuisine categories
- Cuisine images and descriptions
- Shop type association (restaurant/grocery)

### 📱 Banners Management (`/super_admin/banners`)
**Endpoints**:
- `getBanners` - List promotional banners
- `createBanner` - Create new banner
- `editBanner` - Update banner details
- `deleteBanner` - Remove banner

**Features**:
- Create promotional banners
- Banner actions and navigation
- Image management
- Banner scheduling

### 🏷️ Restaurant Sections (`/super_admin/sections`)
**Endpoints**:
- `getSections` - List restaurant sections
- `createSection` - Create new section
- `editSection` - Update section details
- `deleteSection` - Remove section

**Features**:
- Organize restaurants into sections
- Featured restaurant management
- Section-based restaurant grouping

### 💰 Withdraw Requests (`/super_admin/withdraw`)
**Endpoints**:
- `getAllWithdrawRequests` - List withdrawal requests
- `updateWithdrawRequestStatus` - Approve/reject requests

**Features**:
- Manage rider withdrawal requests
- Approve or reject withdrawal requests
- Track rider wallet balances
- Payment processing

### ⚙️ Configuration (`/super_admin/configuration`)
**Endpoints**:
- `getConfiguration` - Get system configuration
- `saveConfiguration` - Update system settings

**Features**:
- Payment gateway configuration (Stripe, PayPal)
- Email service settings (SendGrid)
- SMS service settings (Twilio)
- Google Maps API configuration
- Firebase settings
- Currency and delivery rate settings
- Sentry error tracking configuration

## 🏪 Vendor/Restaurant Pages

### 📊 Restaurant Dashboard (`/admin/dashboard`)
**Endpoints**:
- `getDashboardTotal` - Restaurant-specific statistics
- `getDashboardSales` - Restaurant sales data
- `getDashboardOrders` - Restaurant order analytics

**Features**:
- Restaurant-specific analytics
- Sales performance charts
- Order status overview
- Revenue tracking

### 🍕 Food Management (`/admin/food`)
**Endpoints**:
- `getRestaurantDetail` - Restaurant menu and food items
- `createFood` - Add new food item
- `editFood` - Update food details
- `deleteFood` - Remove food item
- `toggleFoodStatus` - Enable/disable food item

**Features**:
- Manage restaurant menu items
- Food categories and variations
- Pricing and discounts
- Food availability status
- Image management

### 📂 Categories (`/admin/category`)
**Endpoints**:
- `getRestaurantDetail` - Get categories for restaurant
- `createCategory` - Add new category
- `editCategory` - Update category details
- `deleteCategory` - Remove category

**Features**:
- Organize menu into categories
- Category ordering and management
- Category images and descriptions

### 📋 Orders Management (`/admin/orders`)
**Endpoints**:
- `getOrdersByRestId` - Restaurant-specific orders
- `updateStatus` - Update order status
- `assignRider` - Assign delivery rider

**Features**:
- View restaurant orders
- Order status management
- Customer information
- Order details and items
- Delivery tracking

### ⚙️ Restaurant Profile (`/admin/profile`)
**Endpoints**:
- `getRestaurantProfile` - Restaurant details
- `editRestaurant` - Update restaurant information

**Features**:
- Restaurant information management
- Operating hours configuration
- Delivery settings
- Contact information
- Location and delivery bounds

### 🎛️ Options & Addons (`/admin/option`, `/admin/addon`)
**Endpoints**:
- `getRestaurantDetail` - Get options and addons
- `createOption` - Add new option
- `createAddon` - Add new addon
- `editOption` - Update option details
- `editAddon` - Update addon details

**Features**:
- Manage food options (size, type, etc.)
- Manage addons (extra toppings, sides)
- Pricing for options and addons
- Quantity limits for addons

## 🔐 Authentication & Security

### Login System (`/auth/login`)
**Endpoints**:
- `ownerLogin` - User authentication

**Features**:
- Email/password authentication
- Role-based redirection
- Remember me functionality
- Password reset capability

### User Roles
- **ADMIN**: Super admin with full access
- **VENDOR**: Restaurant owner with limited access
- **RESTAURANT**: Individual restaurant management

## 🛠️ Installation & Setup

```bash
# Clone the repository
git clone https://github.com/heart-attack-egypt/admin-panel.git

# Install dependencies
npm install

# Set up environment variables
cp .env.example .env.development

# Start development server
npm run start:dev

# Build for production
npm run build:prod
```

## 🌍 Environment Configuration

The application supports multiple environments:
- **Development**: `.env.development`
- **Staging**: `.env.staging`
- **Production**: `.env.production`

Required environment variables:
- `REACT_APP_GRAPHQL_URL` - GraphQL API endpoint
- `REACT_APP_GOOGLE_MAPS_KEY` - Google Maps API key
- `REACT_APP_FIREBASE_*` - Firebase configuration
- `REACT_APP_SENTRY_DSN` - Error tracking

## 📱 Mobile Responsiveness

The admin panel is built with mobile-first principles:
- Responsive sidebar navigation
- Touch-friendly interface
- Optimized for tablets and mobile devices
- Progressive Web App (PWA) capabilities

## 🔧 Development Scripts

```bash
npm start              # Development server
npm run build          # Production build
npm test              # Run tests
npm run lint:fix      # Fix linting issues
npm run format        # Format code with Prettier
```

## 📊 Key Features

- **Real-time Updates**: Live order status updates
- **Analytics Dashboard**: Comprehensive business insights
- **Multi-language Support**: i18n internationalization
- **Role-based Access**: Secure user role management
- **Responsive Design**: Mobile-first approach
- **Error Tracking**: Sentry integration
- **Push Notifications**: Firebase messaging
- **Map Integration**: Google Maps for delivery tracking

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Run tests and linting
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License.
