import { StatsCard } from "@/components/StatsCard";
import { DashboardC<PERSON> } from "@/components/DashboardChart";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Users, Store, Car, Utensils, TrendingUp, ArrowRight } from "lucide-react";

export default function Dashboard() {
  return (
    <div className="space-y-8">
      {/* Welcome Section */}
      <div className="relative overflow-hidden">
        <Card className="bg-gradient-to-r from-orange-500 via-orange-600 to-red-500 border-0 shadow-xl">
          <CardContent className="p-8">
            <div className="flex items-center justify-between relative z-10">
              <div className="space-y-4">
                <div className="space-y-2">
                  <h1 className="text-3xl font-bold text-white">Welcome back, Admin!</h1>
                  <p className="text-orange-100 text-lg">Your food delivery empire at a glance</p>
                </div>
                <Button className="bg-white text-orange-600 hover:bg-orange-50 font-semibold px-6 py-2 rounded-lg shadow-md transition-all duration-200 hover:shadow-lg">
                  View Analytics <ArrowRight className="w-4 h-4 ml-2" />
                </Button>
              </div>
              <div className="hidden md:block">
                <div className="w-32 h-32 bg-white/10 backdrop-blur-sm rounded-3xl flex items-center justify-center">
                  <div className="text-6xl">🚀</div>
                </div>
              </div>
            </div>
            {/* Decorative elements */}
            <div className="absolute top-0 right-0 w-64 h-64 bg-white/5 rounded-full -translate-y-32 translate-x-32"></div>
            <div className="absolute bottom-0 left-0 w-48 h-48 bg-white/5 rounded-full translate-y-24 -translate-x-24"></div>
          </CardContent>
        </Card>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <StatsCard
          title="Total Users"
          value="2,847"
          icon={Users}
          color="blue"
        />
        <StatsCard
          title="Active Vendors"
          value="156"
          icon={Store}
          color="green"
        />
        <StatsCard
          title="Restaurants"
          value="89"
          icon={Utensils}
          color="orange"
        />
        <StatsCard
          title="Delivery Riders"
          value="234"
          icon={Car}
          color="purple"
        />
      </div>

      {/* Chart and Quick Actions */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <div className="lg:col-span-2">
          <DashboardChart />
        </div>
        
        <Card className="border-0 shadow-md bg-gradient-to-br from-white to-gray-50">
          <CardHeader className="pb-4">
            <CardTitle className="text-xl font-bold text-gray-900 flex items-center gap-2">
              <TrendingUp className="w-5 h-5 text-orange-500" />
              Quick Actions
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            <Button className="w-full bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700 text-white font-semibold py-3 rounded-lg shadow-md transition-all duration-200 hover:shadow-lg">
              Add New Restaurant
            </Button>
            <Button className="w-full bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white font-semibold py-3 rounded-lg shadow-md transition-all duration-200 hover:shadow-lg">
              Add New Vendor
            </Button>
            <Button className="w-full bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white font-semibold py-3 rounded-lg shadow-md transition-all duration-200 hover:shadow-lg">
              Add New Rider
            </Button>
            <Button variant="outline" className="w-full border-2 border-gray-200 hover:border-orange-300 hover:bg-orange-50 font-semibold py-3 rounded-lg transition-all duration-200">
              View All Orders
            </Button>
          </CardContent>
        </Card>
      </div>

      {/* Recent Activity */}
      <Card className="border-0 shadow-md bg-white">
        <CardHeader>
          <CardTitle className="text-xl font-bold text-gray-900">Recent Activity</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {[
              { action: "New order from McDonald's", time: "2 minutes ago", status: "success" },
              { action: "Rider John completed delivery", time: "5 minutes ago", status: "success" },
              { action: "New vendor registration", time: "10 minutes ago", status: "info" },
              { action: "Payment processed", time: "15 minutes ago", status: "success" },
            ].map((activity, index) => (
              <div key={index} className="flex items-center justify-between p-3 rounded-lg bg-gray-50 hover:bg-gray-100 transition-colors">
                <div className="flex items-center gap-3">
                  <div className={`w-2 h-2 rounded-full ${
                    activity.status === 'success' ? 'bg-green-500' : 'bg-blue-500'
                  }`}></div>
                  <span className="text-gray-900 font-medium">{activity.action}</span>
                </div>
                <span className="text-gray-500 text-sm">{activity.time}</span>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
