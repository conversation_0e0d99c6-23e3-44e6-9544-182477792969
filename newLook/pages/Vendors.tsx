
import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Search, Eye, EyeOff, MoreHorizontal, Plus, Store } from "lucide-react";

export default function Vendors() {
  const [showPassword, setShowPassword] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");

  const vendors = [
    {
      id: 1,
      email: "<EMAIL>",
      totalRestaurants: 1,
      status: "active",
      joinDate: "Jan 15, 2024"
    }
  ];

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="space-y-1">
          <h1 className="text-3xl font-bold text-gray-900">Vendor Management</h1>
          <p className="text-gray-600">Manage and oversee your vendor network</p>
        </div>
        <Button className="bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700 text-white font-semibold px-6 py-2 rounded-lg shadow-md">
          <Plus className="w-4 h-4 mr-2" />
          Quick Add
        </Button>
      </div>

      <div className="grid grid-cols-1 xl:grid-cols-3 gap-6">
        {/* Add Vendor Form */}
        <div className="xl:col-span-1">
          <Card className="border-0 shadow-lg bg-white">
            <CardHeader className="bg-gradient-to-r from-orange-500 to-orange-600 text-white rounded-t-lg">
              <CardTitle className="text-lg font-semibold text-center flex items-center justify-center gap-2">
                <Store className="w-5 h-5" />
                Add New Vendor
              </CardTitle>
            </CardHeader>
            <CardContent className="p-6 space-y-5">
              <div className="space-y-2">
                <Label htmlFor="email" className="text-sm font-semibold text-gray-700">Email Address</Label>
                <Input 
                  id="email" 
                  type="email" 
                  placeholder="<EMAIL>" 
                  className="border-gray-300 focus:border-orange-500 focus:ring-orange-500 rounded-lg"
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="password" className="text-sm font-semibold text-gray-700">Password</Label>
                <div className="relative">
                  <Input 
                    id="password" 
                    type={showPassword ? "text" : "password"}
                    placeholder="Create secure password" 
                    className="border-gray-300 focus:border-orange-500 focus:ring-orange-500 pr-10 rounded-lg"
                  />
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    className="absolute right-0 top-0 h-full px-3 hover:bg-transparent"
                    onClick={() => setShowPassword(!showPassword)}
                  >
                    {showPassword ? <EyeOff className="w-4 h-4 text-gray-400" /> : <Eye className="w-4 h-4 text-gray-400" />}
                  </Button>
                </div>
              </div>
              
              <Button className="w-full bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700 text-white font-semibold py-3 rounded-lg shadow-md transition-all duration-200 hover:shadow-lg">
                Create Vendor Account
              </Button>
            </CardContent>
          </Card>
        </div>

        {/* Stats and Illustration */}
        <div className="xl:col-span-2 space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Card className="border-0 shadow-md bg-gradient-to-br from-blue-50 to-blue-100">
              <CardContent className="p-4 text-center">
                <div className="text-2xl font-bold text-blue-600">156</div>
                <div className="text-sm text-blue-700 font-medium">Total Vendors</div>
              </CardContent>
            </Card>
            <Card className="border-0 shadow-md bg-gradient-to-br from-green-50 to-green-100">
              <CardContent className="p-4 text-center">
                <div className="text-2xl font-bold text-green-600">89</div>
                <div className="text-sm text-green-700 font-medium">Active This Month</div>
              </CardContent>
            </Card>
            <Card className="border-0 shadow-md bg-gradient-to-br from-orange-50 to-orange-100">
              <CardContent className="p-4 text-center">
                <div className="text-2xl font-bold text-orange-600">234</div>
                <div className="text-sm text-orange-700 font-medium">Total Restaurants</div>
              </CardContent>
            </Card>
          </div>

          <Card className="border-0 shadow-md bg-gradient-to-br from-gray-50 to-white">
            <CardContent className="p-8 flex items-center justify-center">
              <div className="text-center space-y-4">
                <div className="w-24 h-24 bg-gradient-to-br from-orange-200 to-orange-300 rounded-full flex items-center justify-center mx-auto">
                  <Store className="w-12 h-12 text-orange-600" />
                </div>
                <div className="space-y-2">
                  <h3 className="text-xl font-bold text-gray-900">Vendor Ecosystem</h3>
                  <p className="text-gray-600 max-w-md">Streamline vendor onboarding and management with our comprehensive platform</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Vendors Table */}
      <Card className="border-0 shadow-lg bg-white">
        <CardHeader className="border-b border-gray-100">
          <div className="flex items-center justify-between">
            <CardTitle className="text-xl font-bold text-gray-900">Vendor Directory</CardTitle>
            <div className="flex items-center gap-3">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                <Input
                  placeholder="Search vendors..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10 w-72 border-gray-300 focus:border-orange-500 focus:ring-orange-500 rounded-lg"
                />
              </div>
            </div>
          </div>
        </CardHeader>
        <CardContent className="p-0">
          <Table>
            <TableHeader>
              <TableRow className="bg-gradient-to-r from-gray-50 to-gray-100 hover:from-gray-100 hover:to-gray-150">
                <TableHead className="text-gray-700 font-bold py-4 px-6">Vendor Email</TableHead>
                <TableHead className="text-gray-700 font-bold">Restaurants</TableHead>
                <TableHead className="text-gray-700 font-bold">Join Date</TableHead>
                <TableHead className="text-gray-700 font-bold">Status</TableHead>
                <TableHead className="text-gray-700 font-bold text-center">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {vendors.map((vendor) => (
                <TableRow key={vendor.id} className="hover:bg-gray-50 transition-colors">
                  <TableCell className="font-semibold text-gray-900 py-4 px-6">{vendor.email}</TableCell>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                        <span className="text-blue-600 font-bold text-sm">{vendor.totalRestaurants}</span>
                      </div>
                      <span className="text-gray-600">restaurants</span>
                    </div>
                  </TableCell>
                  <TableCell className="text-gray-600">{vendor.joinDate}</TableCell>
                  <TableCell>
                    <Badge className="bg-green-100 text-green-800 hover:bg-green-200 font-medium">
                      Active
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center justify-center gap-2">
                      <Button 
                        variant="outline" 
                        size="sm" 
                        className="bg-orange-50 border-orange-200 text-orange-700 hover:bg-orange-100 hover:border-orange-300 rounded-lg"
                      >
                        View Restaurants
                      </Button>
                      <Button variant="ghost" size="sm" className="hover:bg-gray-100 rounded-lg">
                        <MoreHorizontal className="w-4 h-4" />
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
          
          <div className="flex items-center justify-between p-4 border-t border-gray-100 bg-gray-50">
            <div className="text-sm text-gray-600">
              Showing 1-1 of 1 vendors
            </div>
            <div className="flex items-center gap-2 text-sm">
              <Button variant="outline" size="sm" disabled className="rounded-lg">
                Previous
              </Button>
              <div className="px-3 py-1 bg-orange-100 text-orange-700 rounded-lg font-medium">1</div>
              <Button variant="outline" size="sm" disabled className="rounded-lg">
                Next
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
