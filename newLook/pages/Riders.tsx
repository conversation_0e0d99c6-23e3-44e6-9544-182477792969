
import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Search, Eye, EyeOff, MoreHorizontal } from "lucide-react";

export default function Riders() {
  const [showPassword, setShowPassword] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");
  const [available, setAvailable] = useState(false);

  const riders = [
    {
      id: 1,
      name: "bedo",
      username: "bedo",
      password: "123",
      phone: "01001247401",
      zone: "egypt",
      available: true
    }
  ];

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Riders</h1>
          <p className="text-gray-600 mt-1">Manage delivery riders</p>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Add Rider Form */}
        <Card className="border-0 shadow-sm">
          <CardHeader className="bg-orange-500 text-white rounded-t-lg">
            <CardTitle className="text-lg font-semibold text-center">Add Rider</CardTitle>
          </CardHeader>
          <CardContent className="p-6 space-y-4">
            <div className="flex items-center justify-between mb-4">
              <Label htmlFor="available" className="text-sm font-medium text-gray-700">Available</Label>
              <Switch
                id="available"
                checked={available}
                onCheckedChange={setAvailable}
              />
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="name" className="text-sm font-medium text-gray-700">Name</Label>
                <Input 
                  id="name" 
                  placeholder="Rider name" 
                  className="border-gray-300 focus:border-orange-500 focus:ring-orange-500"
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="username" className="text-sm font-medium text-gray-700">Username</Label>
                <Input 
                  id="username" 
                  placeholder="Username" 
                  className="border-gray-300 focus:border-orange-500 focus:ring-orange-500"
                />
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="password" className="text-sm font-medium text-gray-700">Password</Label>
                <div className="relative">
                  <Input 
                    id="password" 
                    type={showPassword ? "text" : "password"}
                    placeholder="Password" 
                    className="border-gray-300 focus:border-orange-500 focus:ring-orange-500 pr-10"
                  />
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    className="absolute right-0 top-0 h-full px-3 hover:bg-transparent"
                    onClick={() => setShowPassword(!showPassword)}
                  >
                    {showPassword ? <EyeOff className="w-4 h-4 text-gray-400" /> : <Eye className="w-4 h-4 text-gray-400" />}
                  </Button>
                </div>
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="number" className="text-sm font-medium text-gray-700">Number</Label>
                <Input 
                  id="number" 
                  placeholder="Phone Number" 
                  className="border-gray-300 focus:border-orange-500 focus:ring-orange-500"
                />
              </div>
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="zone" className="text-sm font-medium text-gray-700">Rider Zone</Label>
              <Select>
                <SelectTrigger className="border-gray-300 focus:border-orange-500 focus:ring-orange-500">
                  <SelectValue placeholder="Rider Zone" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="egypt">Egypt</SelectItem>
                  <SelectItem value="cairo">Cairo</SelectItem>
                  <SelectItem value="alexandria">Alexandria</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            <Button className="w-full bg-orange-500 hover:bg-orange-600 text-white font-medium">
              SAVE
            </Button>
          </CardContent>
        </Card>

        {/* Illustration Card */}
        <Card className="border-0 shadow-sm">
          <CardContent className="p-6 flex items-center justify-center">
            <div className="text-center">
              <div className="w-48 h-48 bg-green-100 rounded-full flex items-center justify-center mb-4">
                <div className="text-6xl">🛵</div>
              </div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">Rider Management</h3>
              <p className="text-gray-600">Manage your delivery fleet efficiently</p>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Riders Table */}
      <Card className="border-0 shadow-sm">
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="text-lg font-semibold text-gray-900">Riders</CardTitle>
            <div className="flex items-center gap-2">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                <Input
                  placeholder="Search"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10 w-64 border-gray-300 focus:border-orange-500 focus:ring-orange-500"
                />
              </div>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow className="bg-green-50">
                <TableHead className="text-green-700 font-semibold">Name</TableHead>
                <TableHead className="text-green-700 font-semibold">Username</TableHead>
                <TableHead className="text-green-700 font-semibold">Password</TableHead>
                <TableHead className="text-green-700 font-semibold">Phone</TableHead>
                <TableHead className="text-green-700 font-semibold">Zone</TableHead>
                <TableHead className="text-green-700 font-semibold">Available</TableHead>
                <TableHead className="text-green-700 font-semibold">Action</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {riders.map((rider) => (
                <TableRow key={rider.id} className="hover:bg-gray-50">
                  <TableCell className="font-medium">{rider.name}</TableCell>
                  <TableCell>{rider.username}</TableCell>
                  <TableCell>{rider.password}</TableCell>
                  <TableCell>{rider.phone}</TableCell>
                  <TableCell>{rider.zone}</TableCell>
                  <TableCell>
                    <div className={`w-3 h-3 rounded-full ${rider.available ? 'bg-green-500' : 'bg-gray-400'}`}></div>
                  </TableCell>
                  <TableCell>
                    <Button variant="ghost" size="sm">
                      <MoreHorizontal className="w-4 h-4" />
                    </Button>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
          
          <div className="flex items-center justify-between mt-4 pt-4 border-t">
            <div className="text-sm text-gray-600">
              Rows per page: 10 ▼
            </div>
            <div className="flex items-center gap-2 text-sm text-gray-600">
              <span>1-1 of 1</span>
              <div className="flex gap-1">
                <Button variant="ghost" size="sm" disabled>⟪</Button>
                <Button variant="ghost" size="sm" disabled>⟨</Button>
                <Button variant="ghost" size="sm" disabled>⟩</Button>
                <Button variant="ghost" size="sm" disabled>⟫</Button>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
