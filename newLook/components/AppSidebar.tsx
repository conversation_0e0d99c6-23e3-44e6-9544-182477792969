
import { 
  Home, 
  Store, 
  Users, 
  Utensils,
  Settings,
  Ticket,
  ChefHat,
  Tag,
  MapPin,
  Send,
  Bell,
  DollarSign,
  Car,
  ShoppingBag
} from "lucide-react";
import { NavLink } from "react-router-dom";
import {
  Sidebar,
  SidebarContent,
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  useSidebar,
} from "@/components/ui/sidebar";

const generalItems = [
  { title: "Home", url: "/", icon: Home },
  { title: "Vendors", url: "/vendors", icon: Store },
  { title: "Restaurants", url: "/restaurants", icon: Utensils },
  { title: "Restaurant Sections", url: "/restaurant-sections", icon: ChefHat },
  { title: "Users", url: "/users", icon: Users },
  { title: "Riders", url: "/riders", icon: Car },
];

const managementItems = [
  { title: "Configuration", url: "/configuration", icon: Settings },
  { title: "Coupons", url: "/coupons", icon: Ticket },
  { title: "Cuisines", url: "/cuisines", icon: ChefHat },
  { title: "Banners", url: "/banners", icon: Tag },
  { title: "Tipping", url: "/tipping", icon: DollarSign },
  { title: "Zone", url: "/zone", icon: MapPin },
  { title: "Dispatch", url: "/dispatch", icon: Send },
  { title: "Notifications", url: "/notifications", icon: Bell },
  { title: "Commission Rates", url: "/commission-rates", icon: DollarSign },
  { title: "Withdraw Requests", url: "/withdraw-requests", icon: DollarSign },
  { title: "All Orders", url: "/all-orders", icon: ShoppingBag },
];

export function AppSidebar() {
  const { state } = useSidebar();
  const isCollapsed = state === "collapsed";

  const getNavClass = ({ isActive }: { isActive: boolean }) =>
    isActive 
      ? "bg-gradient-to-r from-orange-100 to-orange-50 text-orange-700 border-r-3 border-orange-500 font-semibold shadow-sm" 
      : "text-gray-700 hover:bg-gradient-to-r hover:from-gray-50 hover:to-gray-100 hover:text-gray-900 transition-all duration-200";

  return (
    <Sidebar className="border-r border-gray-200 bg-white shadow-sm" collapsible="icon">
      <SidebarContent className="bg-gradient-to-b from-white to-gray-50/50">
        <div className="p-4 border-b border-gray-100">
          <div className="flex items-center gap-3">
            <div className="w-10 h-10 bg-gradient-to-br from-orange-500 to-orange-600 rounded-xl flex items-center justify-center shadow-md">
              <span className="text-white font-bold text-lg">🍽️</span>
            </div>
            {!isCollapsed && (
              <div>
                <h2 className="font-bold text-gray-900 text-lg">Heart Attack</h2>
                <p className="text-xs text-gray-500">Food Delivery</p>
              </div>
            )}
          </div>
        </div>

        <SidebarGroup>
          <SidebarGroupLabel className="text-xs font-semibold text-gray-500 uppercase tracking-wider px-4 py-3">
            General
          </SidebarGroupLabel>
          <SidebarGroupContent className="px-2">
            <SidebarMenu className="space-y-1">
              {generalItems.map((item) => (
                <SidebarMenuItem key={item.title}>
                  <SidebarMenuButton asChild className="rounded-lg">
                    <NavLink to={item.url} end className={getNavClass}>
                      <item.icon className="w-5 h-5" />
                      {!isCollapsed && <span className="font-medium">{item.title}</span>}
                    </NavLink>
                  </SidebarMenuButton>
                </SidebarMenuItem>
              ))}
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>

        <SidebarGroup>
          <SidebarGroupLabel className="text-xs font-semibold text-gray-500 uppercase tracking-wider px-4 py-3">
            Management
          </SidebarGroupLabel>
          <SidebarGroupContent className="px-2">
            <SidebarMenu className="space-y-1">
              {managementItems.map((item) => (
                <SidebarMenuItem key={item.title}>
                  <SidebarMenuButton asChild className="rounded-lg">
                    <NavLink to={item.url} className={getNavClass}>
                      <item.icon className="w-5 h-5" />
                      {!isCollapsed && <span className="font-medium">{item.title}</span>}
                    </NavLink>
                  </SidebarMenuButton>
                </SidebarMenuItem>
              ))}
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>
      </SidebarContent>
    </Sidebar>
  );
}
