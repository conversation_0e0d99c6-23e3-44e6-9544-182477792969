
import { Card, CardContent, <PERSON><PERSON>eader, CardTitle } from "@/components/ui/card";
import { LineChart, Line, XAxis, YAxis, CartesianGrid, ResponsiveContainer, Legend } from "recharts";

const data = [
  { month: "Jan", restaurants: 2, vendors: 8, riders: 4, users: 5 },
  { month: "Feb", restaurants: 4, vendors: 7, riders: 3, users: 6 },
  { month: "Mar", restaurants: 6, vendors: 5, riders: 5, users: 4 },
  { month: "Apr", restaurants: 3, vendors: 2, riders: 8, users: 3 },
  { month: "May", restaurants: 7, vendors: 4, riders: 6, users: 4 },
  { month: "Jun", restaurants: 4, vendors: 3, riders: 7, users: 2 },
  { month: "Jul", restaurants: 2, vendors: 2, riders: 5, users: 7 },
];

export function DashboardChart() {
  return (
    <Card className="col-span-2 border-0 shadow-sm">
      <CardHeader>
        <CardTitle className="text-lg font-semibold text-gray-900">Growth Overview</CardTitle>
      </CardHeader>
      <CardContent>
        <ResponsiveContainer width="100%" height={300}>
          <LineChart data={data}>
            <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
            <XAxis dataKey="month" stroke="#666" />
            <YAxis stroke="#666" />
            <Legend />
            <Line 
              type="monotone" 
              dataKey="restaurants" 
              stroke="#f97316" 
              strokeWidth={2}
              name="Restaurants"
              dot={{ fill: "#f97316", strokeWidth: 2, r: 4 }}
            />
            <Line 
              type="monotone" 
              dataKey="vendors" 
              stroke="#dc2626" 
              strokeWidth={2}
              name="Vendors"
              dot={{ fill: "#dc2626", strokeWidth: 2, r: 4 }}
            />
            <Line 
              type="monotone" 
              dataKey="riders" 
              stroke="#1f2937" 
              strokeWidth={2}
              name="Riders"
              dot={{ fill: "#1f2937", strokeWidth: 2, r: 4 }}
            />
            <Line 
              type="monotone" 
              dataKey="users" 
              stroke="#eab308" 
              strokeWidth={2}
              name="Users"
              dot={{ fill: "#eab308", strokeWidth: 2, r: 4 }}
            />
          </LineChart>
        </ResponsiveContainer>
      </CardContent>
    </Card>
  );
}
