
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { LucideIcon } from "lucide-react";

interface StatsCardProps {
  title: string;
  value: string | number;
  icon: LucideIcon;
  trend?: "up" | "down";
  color?: "orange" | "green" | "blue" | "purple";
}

export function StatsCard({ title, value, icon: Icon, trend, color = "orange" }: StatsCardProps) {
  const colorClasses = {
    orange: "bg-orange-50 text-orange-600 border-orange-200",
    green: "bg-green-50 text-green-600 border-green-200",
    blue: "bg-blue-50 text-blue-600 border-blue-200",
    purple: "bg-purple-50 text-purple-600 border-purple-200",
  };

  return (
    <Card className="border-0 shadow-sm hover:shadow-md transition-shadow">
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-sm font-medium text-gray-600">{title}</CardTitle>
        <div className={`p-2 rounded-lg ${colorClasses[color]}`}>
          <Icon className="w-4 h-4" />
        </div>
      </CardHeader>
      <CardContent>
        <div className="text-2xl font-bold text-gray-900">{value}</div>
        <div className="flex items-center mt-2">
          <div className={`w-12 h-2 rounded-full ${color === 'orange' ? 'bg-orange-200' : color === 'green' ? 'bg-green-200' : 'bg-blue-200'}`}>
            <div className={`h-full w-8 rounded-full ${color === 'orange' ? 'bg-orange-500' : color === 'green' ? 'bg-green-500' : 'bg-blue-500'}`}></div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
