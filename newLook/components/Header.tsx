
import { SidebarTrigger } from "@/components/ui/sidebar";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON><PERSON>, <PERSON>, Search } from "lucide-react";
import { Input } from "@/components/ui/input";

export function Header() {
  return (
    <header className="h-16 bg-white border-b border-gray-200 flex items-center justify-between px-6 shadow-sm">
      <div className="flex items-center gap-4">
        <SidebarTrigger className="text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg p-2 transition-colors" />
        <div className="flex items-center gap-3">
          <div className="w-8 h-8 bg-gradient-to-br from-orange-500 to-orange-600 rounded-lg flex items-center justify-center shadow-sm">
            <span className="text-white font-bold text-sm">HA</span>
          </div>
          <div>
            <h1 className="font-bold text-lg text-gray-900">Heart Attack</h1>
            <p className="text-xs text-gray-500 -mt-1">Food Delivery System</p>
          </div>
        </div>
      </div>
      
      <div className="flex items-center gap-4">
        <div className="relative hidden md:block">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
          <Input
            placeholder="Search..."
            className="pl-10 w-64 bg-gray-50 border-gray-200 focus:bg-white transition-colors"
          />
        </div>
        
        <Button variant="ghost" size="sm" className="text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg">
          <Bell className="w-4 h-4" />
        </Button>
        
        <Button variant="ghost" className="text-gray-700 hover:text-gray-900 hover:bg-gray-100 gap-2 rounded-lg px-3 py-2">
          <div className="w-7 h-7 bg-gradient-to-br from-gray-300 to-gray-400 rounded-full flex items-center justify-center">
            <User className="w-4 h-4 text-gray-600" />
          </div>
          <span className="font-medium">Admin</span>
        </Button>
      </div>
    </header>
  );
}
